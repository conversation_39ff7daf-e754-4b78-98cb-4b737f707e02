import { describe, it, expect, vi } from 'vitest'
import { parseQRCodeFromFile, validateQRCodeContent } from '../qrcode-parser'

// Mock jsQR
vi.mock('jsqr', () => ({
  default: vi.fn()
}))

describe('QRCode Parser', () => {
  describe('validateQRCodeContent', () => {
    it('should return false for empty content', () => {
      expect(validateQRCodeContent('')).toBe(false)
      expect(validateQRCodeContent('   ')).toBe(false)
    })

    it('should return true for valid content', () => {
      expect(validateQRCodeContent('https://example.com')).toBe(true)
      expect(validateQRCodeContent('some text content')).toBe(true)
    })
  })

  describe('parseQRCodeFromFile', () => {
    it('should handle file reading errors', async () => {
      // Create a mock file that will cause FileReader to fail
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' })
      
      // Mock FileReader to simulate error
      const originalFileReader = global.FileReader
      global.FileReader = vi.fn().mockImplementation(() => ({
        readAsDataURL: vi.fn().mockImplementation(function() {
          setTimeout(() => {
            if (this.onerror) {
              this.onerror(new Error('File read error'))
            }
          }, 0)
        })
      })) as any

      const result = await parseQRCodeFromFile(mockFile)
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('读取文件失败')

      // Restore original FileReader
      global.FileReader = originalFileReader
    })

    it('should handle invalid file content', async () => {
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' })
      
      // Mock FileReader to return null result
      const originalFileReader = global.FileReader
      global.FileReader = vi.fn().mockImplementation(() => ({
        readAsDataURL: vi.fn().mockImplementation(function() {
          setTimeout(() => {
            if (this.onload) {
              this.onload({ target: { result: null } })
            }
          }, 0)
        })
      })) as any

      const result = await parseQRCodeFromFile(mockFile)
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('无法读取图片文件')

      // Restore original FileReader
      global.FileReader = originalFileReader
    })
  })
})
