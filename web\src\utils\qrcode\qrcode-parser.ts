import jsQR from 'jsqr'

/**
 * 二维码解析结果接口
 */
export interface QRCodeParseResult {
  /** 是否解析成功 */
  success: boolean
  /** 解析出的内容 */
  data?: string
  /** 错误信息 */
  error?: string
}

/**
 * 解析图片文件中的二维码内容
 * @param file 图片文件
 * @returns Promise<QRCodeParseResult> 解析结果
 */
export const parseQRCodeFromFile = (file: File): Promise<QRCodeParseResult> => {
  return new Promise((resolve) => {
    // 创建FileReader读取文件
    const reader = new FileReader()
    
    reader.onload = (event) => {
      const imageData = event.target?.result as string
      if (!imageData) {
        resolve({
          success: false,
          error: '无法读取图片文件'
        })
        return
      }

      // 创建Image对象
      const img = new Image()
      
      img.onload = () => {
        try {
          // 创建Canvas来处理图片
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          
          if (!ctx) {
            resolve({
              success: false,
              error: '无法创建Canvas上下文'
            })
            return
          }

          // 设置Canvas尺寸
          canvas.width = img.width
          canvas.height = img.height

          // 将图片绘制到Canvas上
          ctx.drawImage(img, 0, 0)

          // 获取图片数据
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

          // 使用jsQR解析二维码
          const code = jsQR(imageData.data, imageData.width, imageData.height)

          if (code && code.data) {
            resolve({
              success: true,
              data: code.data
            })
          } else {
            resolve({
              success: false,
              error: '未能识别到二维码，请确保图片清晰且包含有效的二维码'
            })
          }
        } catch (error) {
          resolve({
            success: false,
            error: `解析二维码时发生错误: ${error instanceof Error ? error.message : '未知错误'}`
          })
        }
      }

      img.onerror = () => {
        resolve({
          success: false,
          error: '无法加载图片，请确保文件格式正确'
        })
      }

      // 加载图片
      img.src = imageData
    }

    reader.onerror = () => {
      resolve({
        success: false,
        error: '读取文件失败'
      })
    }

    // 开始读取文件
    reader.readAsDataURL(file)
  })
}

/**
 * 验证二维码内容是否有效
 * @param content 二维码内容
 * @returns boolean 是否有效
 */
export const validateQRCodeContent = (content: string): boolean => {
  if (!content || content.trim().length === 0) {
    return false
  }

  // 这里可以添加更多的验证逻辑
  // 例如检查是否是有效的URL、特定格式等
  return true
}
